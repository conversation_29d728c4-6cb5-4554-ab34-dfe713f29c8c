# 💰 Mobile Money Transaction Dashboard

An interactive dashboard for analyzing mobile money transaction patterns and fraud detection using the PaySim dataset. This project follows a structured 4-week development approach from data cleaning to final deployment.

## 🎯 Project Overview

This project analyzes synthetic mobile money transaction data to identify patterns, detect fraud indicators, and provide interactive visualizations for business analysts. The dashboard enables real-time exploration of over 6 million transactions across 5 different transaction types.

### Key Features
- 📊 **Interactive Filtering**: Filter by transaction type, fraud status, and amount ranges
- 📈 **Real-time Metrics**: Live calculation of key performance indicators
- 🎨 **Multiple Visualizations**: Pie charts, histograms, time series analysis
- 🔍 **Transaction Explorer**: Paginated detailed transaction view
- ⚡ **Performance Optimized**: Cached data loading and efficient filtering

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- pip package manager
- 8GB+ RAM (for large dataset processing)

### Installation & Setup

1. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Download the PaySim dataset** (if not included)
   - Download from [Kaggle PaySim Dataset](https://www.kaggle.com/datasets/ealaxi/paysim1)
   - Place the CSV file in the `data/` directory
   - Ensure filename is: `data/PS_20174392719_1491204439457_log.csv`

3. **Run the dashboard**
   ```bash
   streamlit run src/app.py
   ```

4. **Access the dashboard**
   - Open your browser to `http://localhost:8501`
   - The dashboard will load automatically

### Quick Commands
```bash
# One-time setup
pip install -r requirements.txt

# Run dashboard (opens automatically in browser)
streamlit run src/app.py
```

## 📁 Project Structure

```
mobile money dashboard/
├── 📁 data/
│   ├── PS_20174392719_1491204439457_log.csv  # PaySim dataset (6M+ transactions)
│   ├── cleaned_transactions.csv              # Processed dataset
│   ├── data_dictionary.csv                  # Column descriptions
│   └── column_descriptions.csv              # Business-friendly descriptions
├── 📁 notebooks/
│   ├── Week1_Data_Loading_and_Cleaning.ipynb    # Data preprocessing
│   ├── Week2_Exploratory_Data_Analysis.ipynb   # Statistical analysis
│   ├── Week3_Dashboard_Development.ipynb       # Dashboard documentation
│   └── Week4_Final_Polish.ipynb               # Final presentation prep
├── 📁 src/
│   └── app.py                               # Main Streamlit dashboard
├── 📁 docs/
│   └── presentation.md                      # Presentation materials
├── 📋 requirements.txt                      # Python dependencies
└── 📖 README.md                            # This file
```

## 📚 4-Week Development Timeline

### Week 1: Data Loading & Cleaning ✅
- **Objective**: Load and clean the PaySim dataset
- **Notebook**: `Week1_Data_Loading_and_Cleaning.ipynb`
- **Deliverables**:
  - Cleaned dataset (`data/cleaned_transactions.csv`)
  - Data dictionary and quality assessment
  - Initial data exploration
- **Key Tasks**: Handle missing values, data validation, create data dictionary

### Week 2: Exploratory Data Analysis (EDA) ✅
- **Objective**: Analyze transaction patterns and fraud indicators
- **Notebook**: `Week2_Exploratory_Data_Analysis.ipynb`
- **Deliverables**:
  - Statistical analysis and visualizations
  - Fraud pattern identification
  - Temporal trend analysis
- **Key Tasks**: Transaction pattern analysis, fraud detection, balance behavior

### Week 3: Dashboard Development ✅
- **Objective**: Build interactive Streamlit dashboard
- **Notebook**: `Week3_Dashboard_Development.ipynb`
- **Deliverables**:
  - Complete dashboard application (`src/app.py`)
  - Interactive filters and visualizations
  - Performance optimization
- **Key Tasks**: UI development, real-time filtering, user experience

### Week 4: Final Polish & Presentation ✅
- **Objective**: Finalize project and prepare presentation
- **Notebook**: `Week4_Final_Polish.ipynb`
- **Deliverables**:
  - Final presentation materials
  - Complete project documentation
  - Testing and validation
- **Key Tasks**: Bug fixes, presentation prep, final documentation

## 🎯 How to Use This Project

### For Learning (Step-by-Step)
1. **Start with Week 1**: Open `notebooks/Week1_Data_Loading_and_Cleaning.ipynb`
2. **Run all cells** to understand data loading and cleaning process
3. **Continue to Week 2**: Explore `notebooks/Week2_Exploratory_Data_Analysis.ipynb`
4. **Analyze patterns** and understand the fraud detection insights
5. **Review Week 3**: See dashboard development process in `notebooks/Week3_Dashboard_Development.ipynb`
6. **Launch dashboard**: Run `streamlit run src/app.py`
7. **Final review**: Check `notebooks/Week4_Final_Polish.ipynb` for presentation materials

### For Quick Demo
1. **Install dependencies**: `pip install -r requirements.txt`
2. **Launch dashboard**: `streamlit run src/app.py`
3. **Explore features**: Use filters and interact with visualizations
4. **Review insights**: Check the key metrics and patterns

## 📊 Dataset Information

### PaySim Mobile Money Dataset
- **Source**: [Kaggle PaySim Dataset](https://www.kaggle.com/datasets/ealaxi/paysim1)
- **Size**: 6,362,620 transactions
- **Columns**: 11 features including transaction type, amount, balances, fraud labels
- **Time Period**: 744 hours (31 days) of simulated transactions
- **Transaction Types**: CASH_IN, CASH_OUT, DEBIT, PAYMENT, TRANSFER

### Key Columns
- `step`: Time step in simulation (1 step = 1 hour)
- `type`: Transaction type
- `amount`: Transaction amount
- `nameOrig`: Customer who initiated transaction
- `nameDest`: Customer who received transaction
- `oldbalanceOrg`: Initial balance of sender
- `newbalanceOrig`: New balance of sender
- `oldbalanceDest`: Initial balance of receiver
- `newbalanceDest`: New balance of receiver
- `isFraud`: Binary fraud indicator (target variable)

## 🔧 Technical Requirements

### System Requirements
- **Python**: 3.8 or higher
- **RAM**: 8GB+ recommended (dataset is ~500MB)
- **Storage**: 2GB free space
- **Browser**: Modern web browser for dashboard

### Python Dependencies
```
pandas>=1.3.0
numpy>=1.21.0
matplotlib>=3.4.0
seaborn>=0.11.0
plotly>=5.0.0
streamlit>=1.0.0
jupyter>=1.0.0
scikit-learn>=1.0.0
```

## 🚀 Dashboard Features

### Interactive Filters
- **Transaction Type**: Filter by CASH_IN, CASH_OUT, DEBIT, PAYMENT, TRANSFER
- **Fraud Status**: Show all, fraudulent only, or legitimate only
- **Amount Range**: Slider to filter by transaction amounts

### Key Metrics Display
- **Total Transactions**: Count of filtered transactions
- **Total Volume**: Sum of transaction amounts
- **Fraud Rate**: Percentage of fraudulent transactions
- **Average Amount**: Mean transaction value

### Visualizations
- **Transaction Type Distribution**: Interactive pie chart
- **Amount Distribution**: Histogram with hover details
- **Time Series Analysis**: Dual-axis chart showing volume and fraud rate
- **Transaction Details**: Paginated table with search and sort

### Performance Features
- **Data Caching**: Fast loading with Streamlit cache
- **Efficient Filtering**: Optimized pandas operations
- **Responsive Design**: Works on desktop and tablet
- **Real-time Updates**: All components update simultaneously

## 🔍 Key Insights Discovered

### Transaction Patterns
- **Volume Distribution**: CASH_OUT and PAYMENT dominate transaction volume
- **Amount Patterns**: Wide range from small payments to large transfers
- **Temporal Trends**: Transaction volume varies significantly by hour
- **User Behavior**: High frequency of zero-balance accounts

### Fraud Analysis
- **Fraud Rate**: Overall fraud rate is very low (<1%) but significant in absolute numbers
- **Type Concentration**: Fraud is concentrated in TRANSFER and CASH_OUT transactions
- **Amount Correlation**: Fraudulent transactions tend to have higher amounts
- **Temporal Patterns**: Fraud rates vary over time, suggesting cyclical behavior

### Business Implications
- **Risk Management**: Focus fraud detection on TRANSFER and CASH_OUT transactions
- **Monitoring**: Implement real-time monitoring for high-value transactions
- **User Experience**: Consider balance verification for zero-balance accounts
- **Analytics**: Time-based analysis reveals operational patterns

## 🛠️ Troubleshooting

### Common Issues

**Issue**: "Cleaned data file not found"
```bash
# Solution: Run Week 1 notebook first
jupyter notebook notebooks/Week1_Data_Loading_and_Cleaning.ipynb
```

**Issue**: Dashboard is slow or unresponsive
```bash
# Solution: Check available RAM and close other applications
# Consider using data sampling for development
```

**Issue**: Charts not displaying
```bash
# Solution: Ensure plotly is installed
pip install plotly>=5.0.0
```

**Issue**: Port already in use
```bash
# Solution: Use different port
streamlit run src/app.py --server.port 8502
```

**Issue**: Module not found errors
```bash
# Solution: Reinstall requirements
pip install -r requirements.txt --upgrade
```

### Performance Tips
- **Large Dataset**: Consider sampling data for development/testing
- **Memory Usage**: Close other applications when running analysis
- **Browser**: Use Chrome or Firefox for best dashboard performance
- **Network**: Dashboard runs locally, no internet required after setup

## 📈 Project Outcomes

### Technical Achievements
- ✅ Successfully processed 6M+ transaction dataset
- ✅ Built responsive interactive dashboard
- ✅ Implemented efficient data filtering and caching
- ✅ Created comprehensive analysis pipeline
- ✅ Documented entire development process

### Business Value
- 🎯 **Fraud Detection**: Identified key fraud patterns and risk factors
- 📊 **Data Visualization**: Enabled interactive exploration of transaction data
- ⚡ **Real-time Analysis**: Provided tools for rapid pattern identification
- 📋 **Documentation**: Created reusable analysis framework
- 🎓 **Knowledge Transfer**: Comprehensive learning materials

### Skills Demonstrated
- **Data Science**: Data cleaning, EDA, statistical analysis
- **Visualization**: Interactive charts, dashboard design, UX
- **Programming**: Python, pandas, plotly, streamlit
- **Project Management**: Structured 4-week development timeline
- **Documentation**: Comprehensive technical and user documentation

## 🚀 Future Enhancements

### Technical Improvements
- **Machine Learning**: Implement fraud prediction models
- **Real-time Data**: Connect to live transaction streams
- **Database Integration**: Use PostgreSQL/MongoDB for data storage
- **Cloud Deployment**: Deploy on AWS/Azure/GCP
- **API Development**: Create REST API for data access

### Feature Additions
- **Geographic Analysis**: Add location-based transaction mapping
- **Customer Segmentation**: Implement user behavior clustering
- **Alerting System**: Real-time fraud alerts and notifications
- **Advanced Analytics**: Predictive modeling and forecasting
- **Mobile App**: Responsive mobile interface

### Business Applications
- **Risk Management**: Automated fraud scoring system
- **Compliance**: Regulatory reporting and audit trails
- **Operations**: Transaction monitoring and anomaly detection
- **Strategy**: Customer behavior insights and market analysis

## 📞 Support & Contact

### Getting Help
- **Issues**: Check troubleshooting section above
- **Documentation**: Review notebook comments and markdown cells
- **Code**: All code is commented and self-documenting
- **Data**: Dataset documentation available in notebooks

### Project Information
- **Development Time**: 4 weeks (structured timeline)
- **Dataset**: PaySim synthetic mobile money transactions
- **Technology Stack**: Python, Streamlit, Plotly, Pandas
- **License**: Open source (educational use)

---

## 🎓 Educational Use

This project is designed as a comprehensive learning resource for:
- **Data Science Students**: End-to-end project workflow
- **Business Analysts**: Interactive dashboard development
- **Python Developers**: Streamlit and data visualization
- **Finance Professionals**: Fraud detection and risk analysis

### Learning Outcomes
After completing this project, you will understand:
- Data cleaning and preprocessing techniques
- Exploratory data analysis best practices
- Interactive dashboard development
- Fraud detection pattern recognition
- Project documentation and presentation

---

**🎉 Ready to explore mobile money transaction patterns? Start with Week 1!**

```bash
# Quick start command
pip install -r requirements.txt && streamlit run src/app.py
```
