import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np

# Set page config
st.set_page_config(
    page_title="Mobile Money Transaction Dashboard",
    page_icon="💰",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
.main-header {
    font-size: 2.5rem;
    color: #1f77b4;
    text-align: center;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}
.metric-container {
    background: linear-gradient(90deg, #f0f2f6, #ffffff);
    padding: 1rem;
    border-radius: 10px;
    border-left: 5px solid #1f77b4;
    margin: 0.5rem 0;
}
.sidebar .sidebar-content {
    background: linear-gradient(180deg, #f0f2f6, #ffffff);
}
</style>
""", unsafe_allow_html=True)

# Load data with enhanced error handling
@st.cache_data(ttl=3600, show_spinner="Loading transaction data...")
def load_data():
    """Load and cache transaction data with proper error handling"""
    try:
        df = pd.read_csv('data/cleaned_transactions.csv')

        # Ensure proper data types for performance
        if 'type' in df.columns:
            df['type'] = df['type'].astype('category')
        if 'isFraud' in df.columns:
            df['isFraud'] = df['isFraud'].astype('int8')

        return df
    except FileNotFoundError:
        st.error("📁 **Data file not found!**")
        st.info("Please run the Week 1 notebook first to generate the cleaned dataset.")
        st.markdown("**Steps to fix:**")
        st.markdown("1. Open `notebooks/Week1_Data_Loading_and_Cleaning.ipynb`")
        st.markdown("2. Run all cells to generate `data/cleaned_transactions.csv`")
        st.markdown("3. Refresh this dashboard")
        st.stop()
    except Exception as e:
        st.error(f"Error loading data: {str(e)}")
        st.stop()

# Load the data
with st.spinner("Initializing dashboard..."):
    df = load_data()

# Display data info in sidebar
st.sidebar.markdown("### 📊 Dataset Info")
st.sidebar.info(f"""
**Total Transactions:** {len(df):,}
**Date Range:** {df['step'].min()} - {df['step'].max()} hours
**Transaction Types:** {df['type'].nunique()}
**Fraud Rate:** {df['isFraud'].mean()*100:.3f}%
""")

# Enhanced sidebar filters
st.sidebar.markdown("---")
st.sidebar.markdown("### 🎛️ Dashboard Filters")

# Transaction type filter with better UX
st.sidebar.markdown("**Transaction Types**")
transaction_types = ['All'] + sorted(list(df['type'].unique()))
selected_type = st.sidebar.selectbox(
    'Select transaction type:',
    transaction_types,
    help="Filter transactions by type"
)

# Fraud filter with better labels
st.sidebar.markdown("**Fraud Analysis**")
fraud_filter = st.sidebar.radio(
    'Transaction status:',
    ['All Transactions', 'Fraudulent Only', 'Legitimate Only'],
    help="Filter by fraud status"
)

# Amount range filter with better formatting
st.sidebar.markdown("**Amount Range**")
min_amount = float(df['amount'].min())
max_amount = float(df['amount'].max())

# Use log scale for better UX with large ranges
amount_range = st.sidebar.slider(
    'Transaction amount ($):',
    min_value=min_amount,
    max_value=max_amount,
    value=(min_amount, max_amount),
    format="$%.2f",
    help="Filter transactions by amount range"
)

# Time range filter
st.sidebar.markdown("**Time Period**")
time_range = st.sidebar.slider(
    'Time range (hours):',
    min_value=int(df['step'].min()),
    max_value=int(df['step'].max()),
    value=(int(df['step'].min()), int(df['step'].max())),
    help="Filter by time period in simulation"
)

# Enhanced data filtering with progress indication
@st.cache_data(ttl=300)
def apply_filters(df, selected_type, fraud_filter, amount_range, time_range):
    """Apply filters to dataframe with caching for performance"""
    filtered_df = df.copy()

    # Transaction type filter
    if selected_type != 'All':
        filtered_df = filtered_df[filtered_df['type'] == selected_type]

    # Fraud filter
    if fraud_filter == 'Fraudulent Only':
        filtered_df = filtered_df[filtered_df['isFraud'] == 1]
    elif fraud_filter == 'Legitimate Only':
        filtered_df = filtered_df[filtered_df['isFraud'] == 0]

    # Amount range filter
    filtered_df = filtered_df[
        (filtered_df['amount'] >= amount_range[0]) &
        (filtered_df['amount'] <= amount_range[1])
    ]

    # Time range filter
    filtered_df = filtered_df[
        (filtered_df['step'] >= time_range[0]) &
        (filtered_df['step'] <= time_range[1])
    ]

    return filtered_df

# Apply filters
filtered_df = apply_filters(df, selected_type, fraud_filter, amount_range, time_range)

# Show filter results
if len(filtered_df) == 0:
    st.warning("⚠️ No transactions match the current filters. Please adjust your selection.")
    st.stop()

# Main dashboard with enhanced header
st.markdown('<h1 class="main-header">💰 Mobile Money Transaction Dashboard</h1>', unsafe_allow_html=True)
st.markdown("**Real-time analysis of mobile money transactions with fraud detection insights**")
st.markdown("---")

# Enhanced key metrics with delta calculations
st.markdown("## 📊 Key Performance Indicators")

# Calculate baseline metrics for comparison
baseline_fraud_rate = df['isFraud'].mean() * 100
baseline_avg_amount = df['amount'].mean()

col1, col2, col3, col4 = st.columns(4)

with col1:
    total_transactions = len(filtered_df)
    total_baseline = len(df)
    delta_transactions = total_transactions - total_baseline

    st.metric(
        "Total Transactions",
        f"{total_transactions:,}",
        delta=f"{delta_transactions:,}" if delta_transactions != 0 else None,
        help="Number of transactions matching current filters"
    )

with col2:
    total_volume = filtered_df['amount'].sum()
    st.metric(
        "Total Volume",
        f"${total_volume:,.0f}",
        help="Total transaction volume in dollars"
    )

with col3:
    fraud_rate = filtered_df['isFraud'].mean() * 100 if len(filtered_df) > 0 else 0
    delta_fraud = fraud_rate - baseline_fraud_rate

    st.metric(
        "Fraud Rate",
        f"{fraud_rate:.3f}%",
        delta=f"{delta_fraud:+.3f}%" if abs(delta_fraud) > 0.001 else None,
        delta_color="inverse",
        help="Percentage of fraudulent transactions"
    )

with col4:
    avg_amount = filtered_df['amount'].mean() if len(filtered_df) > 0 else 0
    delta_avg = avg_amount - baseline_avg_amount

    st.metric(
        "Average Amount",
        f"${avg_amount:,.2f}",
        delta=f"${delta_avg:+,.2f}" if abs(delta_avg) > 1 else None,
        help="Average transaction amount"
    )

# Enhanced transaction patterns section
st.markdown("---")
st.markdown("## 📈 Transaction Analysis")

col1, col2 = st.columns(2)

with col1:
    # Enhanced transaction type distribution
    type_counts = filtered_df['type'].value_counts()

    fig = px.pie(
        values=type_counts.values,
        names=type_counts.index,
        title='Transaction Type Distribution',
        hole=0.4,
        color_discrete_sequence=px.colors.qualitative.Set3
    )

    fig.update_traces(
        textposition='inside',
        textinfo='percent+label',
        hovertemplate="<b>%{label}</b><br>" +
                     "Count: %{value:,}<br>" +
                     "Percentage: %{percent}<br>" +
                     "<extra></extra>"
    )

    fig.update_layout(
        showlegend=True,
        height=400,
        title_x=0.5
    )

    st.plotly_chart(fig, use_container_width=True)

with col2:
    # Enhanced amount distribution with fraud overlay
    fig = px.histogram(
        filtered_df,
        x='amount',
        color='isFraud',
        title='Transaction Amount Distribution by Fraud Status',
        nbins=50,
        opacity=0.7,
        color_discrete_map={0: '#1f77b4', 1: '#d62728'},
        labels={'isFraud': 'Fraud Status', 'amount': 'Transaction Amount ($)'}
    )

    fig.update_layout(
        height=400,
        title_x=0.5,
        xaxis_title="Transaction Amount ($)",
        yaxis_title="Frequency",
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Update legend labels
    fig.for_each_trace(lambda t: t.update(name="Legitimate" if t.name == "0" else "Fraudulent"))

    st.plotly_chart(fig, use_container_width=True)

# Enhanced time series analysis
st.markdown("---")
st.markdown("## ⏰ Temporal Analysis")

# Create hourly analysis for better insights
filtered_df['hour'] = filtered_df['step'] % 24
hourly_data = filtered_df.groupby('hour').agg({
    'amount': ['sum', 'count'],
    'isFraud': ['sum', 'count']
}).round(2)

hourly_data.columns = ['total_volume', 'transaction_count', 'fraud_count', 'total_count']
hourly_data['fraud_rate'] = (hourly_data['fraud_count'] / hourly_data['total_count'] * 100).fillna(0)
hourly_data = hourly_data.reset_index()

# Create enhanced dual-axis chart
fig = make_subplots(
    specs=[[{"secondary_y": True}]],
    subplot_titles=("Hourly Transaction Volume vs Fraud Rate",)
)

# Add volume bars
fig.add_trace(
    go.Bar(
        x=hourly_data['hour'],
        y=hourly_data['total_volume'],
        name="Transaction Volume ($)",
        opacity=0.7,
        marker_color='#1f77b4',
        hovertemplate="<b>Hour %{x}</b><br>" +
                     "Volume: $%{y:,.0f}<br>" +
                     "Transactions: %{customdata:,}<br>" +
                     "<extra></extra>",
        customdata=hourly_data['transaction_count']
    ),
    secondary_y=False
)

# Add fraud rate line
fig.add_trace(
    go.Scatter(
        x=hourly_data['hour'],
        y=hourly_data['fraud_rate'],
        mode='lines+markers',
        name="Fraud Rate (%)",
        line=dict(color='#d62728', width=3),
        marker=dict(size=8),
        hovertemplate="<b>Hour %{x}</b><br>" +
                     "Fraud Rate: %{y:.2f}%<br>" +
                     "Fraud Count: %{customdata}<br>" +
                     "<extra></extra>",
        customdata=hourly_data['fraud_count']
    ),
    secondary_y=True
)

# Update layout
fig.update_xaxes(
    title_text="Hour of Day",
    tickmode='linear',
    tick0=0,
    dtick=2
)
fig.update_yaxes(title_text="Transaction Volume ($)", secondary_y=False)
fig.update_yaxes(title_text="Fraud Rate (%)", secondary_y=True)

fig.update_layout(
    title='Hourly Transaction Patterns',
    title_x=0.5,
    hovermode='x unified',
    height=500,
    showlegend=True,
    legend=dict(
        orientation="h",
        yanchor="bottom",
        y=1.02,
        xanchor="right",
        x=1
    )
)

st.plotly_chart(fig, use_container_width=True)

# Add insights box
with st.expander("💡 Key Insights from Temporal Analysis"):
    peak_hour = hourly_data.loc[hourly_data['fraud_rate'].idxmax(), 'hour']
    peak_fraud_rate = hourly_data['fraud_rate'].max()
    peak_volume_hour = hourly_data.loc[hourly_data['total_volume'].idxmax(), 'hour']

    st.markdown(f"""
    - **Peak Fraud Hour**: {peak_hour}:00 with {peak_fraud_rate:.2f}% fraud rate
    - **Peak Volume Hour**: {peak_volume_hour}:00 with highest transaction volume
    - **Total Hours Analyzed**: {len(hourly_data)} hours
    - **Average Hourly Fraud Rate**: {hourly_data['fraud_rate'].mean():.3f}%
    """)

# Enhanced transaction details section
st.markdown("---")
st.markdown("## 🔍 Transaction Explorer")

# Add search functionality
search_term = st.text_input("🔎 Search transactions:", placeholder="Enter transaction ID, type, or amount...")

# Apply search filter
display_df = filtered_df.copy()
if search_term:
    # Search across multiple columns
    search_mask = (
        display_df['nameOrig'].astype(str).str.contains(search_term, case=False, na=False) |
        display_df['nameDest'].astype(str).str.contains(search_term, case=False, na=False) |
        display_df['type'].astype(str).str.contains(search_term, case=False, na=False) |
        display_df['amount'].astype(str).str.contains(search_term, case=False, na=False)
    )
    display_df = display_df[search_mask]

# Enhanced pagination controls
col1, col2, col3 = st.columns([1, 1, 2])

with col1:
    rows_per_page = st.selectbox('Rows per page:', [10, 25, 50, 100], index=1)

with col2:
    total_pages = len(display_df) // rows_per_page + (1 if len(display_df) % rows_per_page > 0 else 0)
    if total_pages > 0:
        current_page = st.number_input('Page:', min_value=1, max_value=total_pages, value=1)
    else:
        current_page = 1
        st.write("No results found")

with col3:
    if len(display_df) > 0:
        start_idx = (current_page - 1) * rows_per_page
        end_idx = min(start_idx + rows_per_page, len(display_df))
        st.info(f'📊 Showing {start_idx + 1}-{end_idx} of {len(display_df):,} transactions')
    else:
        st.warning("No transactions match your search criteria")

# Display enhanced dataframe
if len(display_df) > 0:
    # Select and rename columns for better display
    display_columns = {
        'step': 'Time Step',
        'type': 'Type',
        'amount': 'Amount ($)',
        'isFraud': 'Fraud',
        'nameOrig': 'From Account',
        'nameDest': 'To Account',
        'oldbalanceOrg': 'From Balance Before',
        'newbalanceOrig': 'From Balance After',
        'oldbalanceDest': 'To Balance Before',
        'newbalanceDest': 'To Balance After'
    }

    table_df = display_df[list(display_columns.keys())].iloc[start_idx:end_idx].copy()
    table_df = table_df.rename(columns=display_columns)

    # Format the dataframe for better display
    table_df['Amount ($)'] = table_df['Amount ($)'].apply(lambda x: f"${x:,.2f}")
    table_df['Fraud'] = table_df['Fraud'].map({0: '✅ No', 1: '🚨 Yes'})

    # Format balance columns
    for col in ['From Balance Before', 'From Balance After', 'To Balance Before', 'To Balance After']:
        table_df[col] = table_df[col].apply(lambda x: f"${x:,.2f}")

    st.dataframe(
        table_df,
        use_container_width=True,
        height=400
    )

    # Add download button
    csv = display_df.to_csv(index=False)
    st.download_button(
        label="📥 Download filtered data as CSV",
        data=csv,
        file_name=f"mobile_money_transactions_{len(display_df)}_records.csv",
        mime="text/csv"
    )
else:
    st.info("Adjust your filters or search terms to see transaction data.")

# Footer with additional information
st.markdown("---")
st.markdown("### 📋 Dashboard Information")
st.info("""
**About this Dashboard:**
- Built with Streamlit and Plotly for interactive data exploration
- Data source: PaySim synthetic mobile money dataset
- Real-time filtering and analysis capabilities
- Fraud detection insights and pattern analysis

**How to use:**
1. Use the sidebar filters to narrow down transactions
2. Explore the visualizations to understand patterns
3. Use the transaction explorer to examine individual records
4. Download filtered data for further analysis
""")

st.markdown("---")
st.markdown("*Mobile Money Transaction Dashboard - Built with ❤️ using Streamlit*")
